import { supabase } from '@indie-points/lib';
import { AuthError, Session, User } from '@supabase/supabase-js';
import * as AuthSession from 'expo-auth-session';
import { useRouter } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import React, { createContext, useContext, useEffect, useState } from 'react';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (
    email: string,
    password: string
  ) => Promise<{ error: AuthError | null }>;
  signIn: (
    email: string,
    password: string
  ) => Promise<{ error: AuthError | null }>;
  signInWithProvider: (
    provider: 'google' | 'facebook' | 'twitter'
  ) => Promise<{ error: Error | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
  updatePassword: (password: string) => Promise<{ error: AuthError | null }>;
  deleteAccount: () => Promise<{ error: AuthError | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);

      if (event === 'SIGNED_OUT' && !session) {
        router.replace('/sign-in');
      }
    });

    return () => subscription.unsubscribe();
  }, [router]);

  const signUp = async (email: string, password: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
    });
    console.log(error);
    return { error };
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signInWithProvider = async (
    provider: 'google' | 'facebook' | 'twitter'
  ): Promise<{ error: Error | null }> => {
    try {
      const redirectScheme = 'customer-mobile-app';
      const redirectUrl = AuthSession.makeRedirectUri({
        scheme: redirectScheme,
      });

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: { redirectTo: redirectUrl, skipBrowserRedirect: true },
      });

      if (error) return { error };
      if (!data.url)
        return { error: new Error('No authentication URL received') };

      const result = await WebBrowser.openAuthSessionAsync(
        data.url,
        redirectUrl
      );
      if (result.type !== 'success' || !result.url) {
        return {
          error: new Error(
            result.type === 'cancel'
              ? 'Authentication cancelled'
              : 'Authentication failed'
          ),
        };
      }

      const tokenParams = new URLSearchParams(
        result.url.includes('#')
          ? result.url.split('#')[1]
          : result.url.split('?')[1]
      );
      const accessToken = tokenParams.get('access_token');
      const refreshToken = tokenParams.get('refresh_token');

      if (!accessToken)
        return { error: new Error('No authentication tokens received') };

      const { data: sessionData, error: sessionError } =
        await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken ?? '',
        });

      if (sessionError) return { error: sessionError };
      if (!sessionData.session)
        return { error: new Error('Failed to set session') };

      return { error: null };
    } catch (error) {
      return {
        error:
          error instanceof Error
            ? error
            : new Error('An unexpected error occurred'),
      };
    }
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: 'customer-mobile-app://reset-password',
    });
    return { error };
  };

  const updatePassword = async (password: string) => {
    const { error } = await supabase.auth.updateUser({
      password,
    });
    return { error };
  };

  const deleteAccount = async () => {
    try {
      console.log('🔥 Starting account deletion process...');

      if (!user?.id) {
        console.log('❌ No user logged in');
        return {
          error: {
            message: 'No user logged in',
            name: 'NoUserError',
          } as AuthError,
        };
      }

      console.log('👤 Deleting business data for user:', user.id);

      // Delete business data using our database function
      const { data: deleteResult, error: rpcError } = await supabase.rpc(
        'delete_user_account'
      );

      console.log(
        '📡 RPC call completed. Result:',
        deleteResult,
        'Error:',
        rpcError
      );

      if (rpcError) {
        console.error('❌ Error deleting business data:', rpcError);
        return {
          error: {
            message: rpcError.message || 'Failed to delete business data',
            name: 'DeleteAccountError',
          } as AuthError,
        };
      }

      // Check if the function returned an error in the result
      if (deleteResult && !deleteResult.success) {
        console.error(
          '❌ Database function returned error:',
          deleteResult.error
        );
        return {
          error: {
            message: deleteResult.error || 'Failed to delete business data',
            name: 'DeleteAccountError',
          } as AuthError,
        };
      }

      console.log('✅ Business data deleted successfully:', deleteResult);
      console.log('� Signing out user...');

      // Sign out the user normally - this will clear their session
      const { error: signOutError } = await signOut();
      if (signOutError) {
        console.warn('⚠️ Sign out error (continuing anyway):', signOutError);
      }

      console.log('✅ Account closure completed successfully');

      // The signOut() call above should have already cleared the auth state
      // and triggered navigation via the auth state change listener
      return { error: null };
    } catch (error) {
      console.error('💥 Unexpected error deleting account:', error);
      return {
        error:
          error instanceof Error
            ? ({ message: error.message, name: error.name } as AuthError)
            : ({
                message: 'An unexpected error occurred',
                name: 'UnknownError',
              } as AuthError),
      };
    }
  };

  const value = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signInWithProvider,
    signOut,
    resetPassword,
    updatePassword,
    deleteAccount,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
