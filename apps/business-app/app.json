{"expo": {"name": "Indie Points", "orientation": "portrait", "scheme": "business-mobile-app", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": false, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSCameraUsageDescription": "This app uses the camera to scan QR codes."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.CAMERA"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", "expo-web-browser", "expo-camera", "expo-apple-authentication"], "experiments": {"typedRoutes": true}, "extra": {"router": {}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/70a54468-48c4-4ce8-8cb8-461f7b939894"}}}